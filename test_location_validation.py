#!/usr/bin/env python
"""
Test script to verify location validation works with US cities data.
"""

import os
import sys
import django

# Setup Django environment
if __name__ == '__main__':
    # Add the project root to the Python path
    project_root = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, project_root)
    
    # Setup Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings')
    django.setup()

from venues_app.utils import validate_location_combination
from venues_app.models import USCity

def test_location_validation():
    """Test location validation with various inputs."""
    
    print("🧪 Testing Location Validation with US Cities Data")
    print("=" * 60)
    
    # Test cases
    test_cases = [
        # (state, county, city, expected_valid, description)
        ('CA', 'Santa Clara County', 'San Jose', True, 'Santa Clara County with County suffix'),
        ('CA', 'Santa Clara', 'San Jose', True, 'Santa Clara without County suffix'),
        ('NY', 'New York County', 'New York', True, 'New York County with County suffix'),
        ('NY', 'New York', 'New York', True, 'New York without County suffix'),
        ('TX', 'Harris County', 'Houston', True, 'Harris County with County suffix'),
        ('TX', 'Harris', 'Houston', True, 'Harris without County suffix'),
        ('FL', 'Miami-Dade County', 'Miami', True, 'Miami-Dade County with County suffix'),
        ('FL', 'Miami-Dade', 'Miami', True, 'Miami-Dade without County suffix'),
        ('CA', 'Invalid County', 'San Jose', False, 'Invalid county name'),
        ('CA', 'Santa Clara', 'Invalid City', False, 'Invalid city name'),
        ('XX', 'Santa Clara', 'San Jose', False, 'Invalid state code'),
    ]
    
    passed = 0
    failed = 0
    
    for state, county, city, expected_valid, description in test_cases:
        print(f"\n🔍 Testing: {description}")
        print(f"   Input: State={state}, County={county}, City={city}")
        
        try:
            is_valid, uscity, error_message = validate_location_combination(state, county, city)
            
            if is_valid == expected_valid:
                print(f"   ✅ PASS: Validation returned {is_valid} as expected")
                if is_valid and uscity:
                    print(f"      Found: {uscity.city}, {uscity.county_name}, {uscity.state_name}")
                elif not is_valid and error_message:
                    print(f"      Error: {error_message}")
                passed += 1
            else:
                print(f"   ❌ FAIL: Expected {expected_valid}, got {is_valid}")
                if error_message:
                    print(f"      Error: {error_message}")
                failed += 1
                
        except Exception as e:
            print(f"   💥 ERROR: Exception occurred - {str(e)}")
            failed += 1
    
    print(f"\n📊 Test Results:")
    print(f"   ✅ Passed: {passed}")
    print(f"   ❌ Failed: {failed}")
    print(f"   📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    return failed == 0

def show_database_stats():
    """Show statistics about the loaded city data."""
    
    print("\n📊 Database Statistics:")
    print("=" * 30)
    
    total_cities = USCity.objects.count()
    total_states = USCity.objects.values('state_name').distinct().count()
    total_counties = USCity.objects.values('county_name').distinct().count()
    
    print(f"🏙️  Total Cities: {total_cities:,}")
    print(f"🗺️  Total States: {total_states}")
    print(f"🏞️  Total Counties: {total_counties:,}")
    
    # Show some sample data
    print(f"\n🎯 Sample Cities:")
    sample_cities = USCity.objects.filter(
        state_id__in=['CA', 'NY', 'TX', 'FL']
    ).order_by('state_id', 'city')[:10]
    
    for city in sample_cities:
        print(f"   • {city.city}, {city.county_name}, {city.state_name} ({city.state_id})")

def main():
    """Main function to run all tests."""
    
    print("🚀 CozyWish Location Validation Test Suite")
    print("=" * 50)
    
    # Check if city data is loaded
    city_count = USCity.objects.count()
    if city_count == 0:
        print("❌ No city data found in database!")
        print("   Please run: python manage.py seed_us_cities --clear")
        return False
    
    print(f"✅ Found {city_count:,} cities in database")
    
    # Show database statistics
    show_database_stats()
    
    # Run validation tests
    success = test_location_validation()
    
    if success:
        print(f"\n🎉 All tests passed! Location validation is working correctly.")
        print(f"   You can now use the venue creation wizard at:")
        print(f"   http://127.0.0.1:8000/venues/provider/create/wizard/location/")
    else:
        print(f"\n⚠️  Some tests failed. Please check the validation logic.")
    
    return success

if __name__ == '__main__':
    main()
