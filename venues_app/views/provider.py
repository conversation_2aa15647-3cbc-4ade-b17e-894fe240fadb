"""Views for service provider venue and service management."""

# --- Standard Library Imports ---
from datetime import timedelta
import json
import logging
from collections import defaultdict

# --- Django Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.paginator import Paginator
from django.db.models import Avg, Max
from django.db import transaction
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy
from django.views.generic import CreateView, DeleteView, UpdateView
from django.http import JsonResponse, HttpResponseForbidden
from django.views.decorators.http import require_POST
from django.core.exceptions import ValidationError, PermissionDenied
from django.core.cache import cache
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.core.files.storage import default_storage
from django.core.exceptions import ValidationError, PermissionDenied
from django.core.paginator import Paginator
from django.db import transaction, IntegrityError
from django.db.models import Q, Count, Avg, Max, Prefetch
from django.http import JsonResponse, HttpResponseRedirect, Http404, HttpResponseForbidden
from django.urls import reverse, reverse_lazy
from django.utils.decorators import method_decorator
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from django.views import View
from django.views.decorators.http import require_POST, require_http_methods
from django.views.generic import CreateView, UpdateView, DeleteView

# --- Local App Imports ---
from booking_cart_app.models import Booking
from ..forms import (
    VenueFAQForm, ServiceForm, VenueForm, OperatingHoursForm,
    OperatingHoursFormSetFactory, VenueAmenityForm,
    VenueImageForm, VenueGalleryImagesForm, VenueWithOperatingHoursForm,
    SimplifiedOperatingHoursForm, HolidayScheduleForm, VenueVisibilityForm
)
from ..forms.venue import VenueCreateForm
from ..models import (
    Venue, Category, VenueImage, VenueFAQ, VenueAmenity, 
    OperatingHours, HolidaySchedule, USCity, Service, VenueCreationDraft
)
from .common import MAX_FAQS_PER_VENUE, MAX_SERVICES_PER_VENUE, ServiceProviderRequiredMixin
from ..utils import (
    validate_location_combination, 
    find_matching_uscity, 
    get_location_suggestions_with_fuzzy_matching,
    validate_phone_number,
    validate_business_email,
    validate_website_url,
    handle_venue_creation_error,
    ValidationMessages,
    ErrorHandler
)

# --- Additional App Imports ---
from accounts_app.models import ServiceProviderProfile
from ..forms import (
    VenueForm, VenueFAQForm, VenueAmenityForm, VenueImageForm,
    OperatingHoursForm, HolidayScheduleForm
)
from ..forms.venue import VenueCreateForm, VenueWithOperatingHoursForm
from ..forms.operating_hours import SimplifiedOperatingHoursForm
from ..forms.service import ServiceForm
from ..utils import (
    get_approval_progress, get_location_suggestions, requires_reapproval,
    handle_venue_creation_error, validate_phone_number, validate_business_email, 
    validate_website_url, sync_contact_from_service_provider,
    generate_email_verification_token
)


class VenueCreationWizardView:
    """Multi-step venue creation wizard with database-backed progress saving"""
    
    def __init__(self, request, user):
        self.request = request
        self.user = user
        self.service_provider = user.service_provider_profile
    
    def get_or_create_draft(self):
        """Get or create venue creation draft"""
        draft, created = VenueCreationDraft.objects.get_or_create(
            service_provider=self.service_provider,
            defaults={'current_step': 'basic'}
        )
        return draft, created
    
    def save_progress(self, step, form_data):
        """Save current progress to database"""
        draft, created = self.get_or_create_draft()
        
        # Update draft with current form data
        draft.update_from_form_data(form_data, step)
        
        # Also keep cache for backward compatibility (optional)
        cache_key = f'venue_creation_wizard_{self.user.id}'
        progress_data = cache.get(cache_key, {})
        progress_data[step] = form_data
        progress_data['last_step'] = step
        progress_data['progress_percentage'] = draft.get_progress_percentage()
        cache.set(cache_key, progress_data, timeout=3600 * 24)  # 24 hours
        
        return {
            'draft_id': draft.id,
            'progress_percentage': draft.get_progress_percentage(),
            'current_step': draft.current_step,
            'completed_steps': draft.completed_steps,
            'last_updated': draft.updated_at.isoformat() if draft.updated_at else None,
        }
    
    def get_progress(self):
        """Get saved progress from database"""
        try:
            draft = VenueCreationDraft.objects.get(service_provider=self.service_provider)
            return draft.to_dict()
        except VenueCreationDraft.DoesNotExist:
            return {}
    
    def get_draft(self):
        """Get the draft object if it exists"""
        try:
            return VenueCreationDraft.objects.get(service_provider=self.service_provider)
        except VenueCreationDraft.DoesNotExist:
            return None
    
    def clear_progress(self):
        """Clear saved progress from database and cache"""
        try:
            draft = VenueCreationDraft.objects.get(service_provider=self.service_provider)
            draft.delete()
        except VenueCreationDraft.DoesNotExist:
            pass
        
        # Also clear cache
        cache_key = f'venue_creation_wizard_{self.user.id}'
        cache.delete(cache_key)
    
    def auto_save_progress(self, form_data, step):
        """Auto-save progress without validation - for real-time saving"""
        try:
            draft, created = self.get_or_create_draft()
            
            # Clean form data - remove CSRF token and action
            clean_data = {k: v for k, v in form_data.items() 
                         if k not in ['csrfmiddlewaretoken', 'action']}
            
            draft.update_from_form_data(clean_data, step)
            
            return {
                'success': True,
                'draft_id': draft.id,
                'progress_percentage': draft.get_progress_percentage(),
                'last_updated': draft.updated_at.isoformat(),
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _calculate_progress(self, progress_data):
        """Calculate overall completion percentage - updated for 7-step wizard"""
        try:
            draft = VenueCreationDraft.objects.get(service_provider=self.service_provider)
            return draft.get_progress_percentage()
        except VenueCreationDraft.DoesNotExist:
            # Fallback to old calculation
            required_steps = ['basic', 'location', 'contact', 'hours_amenities', 'services']
            completed_steps = sum(1 for step in required_steps if step in progress_data)
            return min(int((completed_steps / len(required_steps)) * 100), 100)

    def get_next_step(self, current_step):
        """Get next step in the 7-step wizard"""
        steps = ['basic', 'location', 'contact', 'hours_amenities', 'services', 'gallery_team', 'review_submit']
        try:
            current_index = steps.index(current_step)
            return steps[current_index + 1] if current_index < len(steps) - 1 else 'review_submit'
        except ValueError:
            return 'basic'

    def get_previous_step(self, current_step):
        """Get previous step in the 7-step wizard"""
        steps = ['basic', 'location', 'contact', 'hours_amenities', 'services', 'gallery_team', 'review_submit']
        try:
            current_index = steps.index(current_step)
            return steps[current_index - 1] if current_index > 0 else None
        except ValueError:
            return None

    def get_step_form_class(self, step):
        """Get the appropriate form class for each step"""
        from ..forms.venue import (
            VenueBasicInfoForm, VenueLocationForm, VenueContactForm,
            VenueHoursAmenitiesForm, VenueServicesForm, VenueGalleryTeamForm,
            VenueReviewSubmitForm
        )

        form_mapping = {
            'basic': VenueBasicInfoForm,
            'location': VenueLocationForm,
            'contact': VenueContactForm,
            'hours_amenities': VenueHoursAmenitiesForm,
            'services': VenueServicesForm,
            'gallery_team': VenueGalleryTeamForm,
            'review_submit': VenueReviewSubmitForm,
        }

        return form_mapping.get(step, VenueBasicInfoForm)

    def validate_step_data(self, step, form_data):
        """Validate data for a specific step"""
        form_class = self.get_step_form_class(step)
        form = form_class(data=form_data)

        if form.is_valid():
            return True, {}
        else:
            return False, form.errors

    def handle_complex_data(self, step, form_data):
        """Handle complex data like services, team members, FAQs"""
        if step == 'hours_amenities':
            # Process operating hours data
            if 'operating_hours' in form_data:
                # Validate operating hours format
                pass

        elif step == 'services':
            # Process services data
            if 'services' in form_data:
                # Validate services format and pricing
                pass

        elif step == 'gallery_team':
            # Process images and team members
            if 'images' in form_data:
                # Handle image uploads and ordering
                pass
            if 'team_members' in form_data:
                # Validate team member data
                pass

        elif step == 'review_submit':
            # Process FAQs and final validation
            if 'faqs' in form_data:
                # Validate FAQ format
                pass

        return form_data


@login_required
def venue_create_wizard_view(request, step='basic'):
    """Multi-step venue creation wizard with progress saving and guided tour."""
    # Ensure user has service provider profile
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'You must have a service provider profile to create a venue.')
        return redirect('accounts_app:service_provider_profile')

    # Check if user already has a venue
    existing_venue = Venue.objects.filter(
        service_provider=request.user.service_provider_profile,
        is_deleted=False
    ).first()

    if existing_venue:
        messages.warning(request, 'You already have a venue. You can only have one venue per account. You can edit your existing venue below.')
        return redirect('venues_app:venue_edit')

    # Initialize wizard
    wizard = VenueCreationWizardView(request, request.user)
    
    # Initialize variables for all code paths to prevent UnboundLocalError
    progress_data = wizard.get_progress()
    draft = wizard.get_draft()
    
    # Handle AJAX requests for progress saving
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return handle_wizard_ajax(request, wizard, step)

    if request.method == 'POST':
        # Handle form submission - merge current POST data with saved progress
        merged_data = progress_data.copy()

        # Convert POST data to dict, handling multiple values correctly
        post_data = {}
        for key, value in request.POST.items():
            if key != 'csrfmiddlewaretoken':
                post_data[key] = value

        merged_data.update(post_data)

        # Get the appropriate form class for the current step
        form_class = wizard.get_step_form_class(step)
        form = form_class(data=merged_data)

        if form.is_valid():
            # Handle complex data processing
            processed_data = wizard.handle_complex_data(step, form.cleaned_data)

            # If this is the final step, create the venue
            if step == 'review_submit':
                return create_venue_from_wizard(request, wizard, form)
            else:
                # Save progress and move to next step
                progress_result = wizard.save_progress(step, processed_data)
                next_step = wizard.get_next_step(step)

                # Get step titles for the new 7-step structure
                step_titles = {
                    'basic': 'Basic Information',
                    'location': 'Location & Address',
                    'contact': 'Contact & Communication',
                    'hours_amenities': 'Operating Hours & Amenities',
                    'services': 'Services & Pricing',
                    'gallery_team': 'Gallery & Team',
                    'review_submit': 'Review & Submit'
                }

                messages.success(
                    request,
                    f'Progress saved! Moving to {step_titles.get(next_step, next_step.title())} step.'
                )

                return redirect('venues_app:venue_create_wizard', step=next_step)
        else:
            # Show validation errors
            for field, errors in form.errors.items():
                for error in errors:
                    # Fix: Properly handle getting field labels
                    if field in form.fields:
                        field_label = form.fields[field].label or field.replace('_', ' ').title()
                    else:
                        field_label = field.replace('_', ' ').title()
                    messages.error(request, f"{field_label}: {error}")
    else:
        # Show message if restoring from draft
        if draft and progress_data:
            time_since_update = timezone.now() - draft.updated_at
            if time_since_update.days > 0:
                time_str = f"{time_since_update.days} day(s) ago"
            elif time_since_update.seconds > 3600:
                hours = time_since_update.seconds // 3600
                time_str = f"{hours} hour(s) ago"
            else:
                minutes = time_since_update.seconds // 60
                time_str = f"{minutes} minute(s) ago"
            
            messages.info(
                request,
                f'✅ Welcome back! We found your saved progress from {time_str}. You can continue where you left off or start fresh.'
            )
        
        # Initialize form with progress data using the appropriate form class
        form_class = wizard.get_step_form_class(step)
        form = form_class(initial=progress_data)

    # Ensure form is initialized if not set in POST error case
    if 'form' not in locals():
        form_class = wizard.get_step_form_class(step)
        form = form_class(initial=progress_data)

    # Prepare context for 7-step wizard
    step_choices = [
        ('basic', 'Basic Information'),
        ('location', 'Location & Address'),
        ('contact', 'Contact & Communication'),
        ('hours_amenities', 'Operating Hours & Amenities'),
        ('services', 'Services & Pricing'),
        ('gallery_team', 'Gallery & Team'),
        ('review_submit', 'Review & Submit'),
    ]

    step_order = ['basic', 'location', 'contact', 'hours_amenities', 'services', 'gallery_team', 'review_submit']

    context = {
        'form': form,
        'current_step': step,
        'step_choices': step_choices,
        'current_step_title': dict(step_choices).get(step, step.title()),
        'progress_data': progress_data,
        'progress_percentage': wizard._calculate_progress(progress_data),
        'next_step': wizard.get_next_step(step),
        'previous_step': wizard.get_previous_step(step),
        'is_first_step': step == 'basic',
        'is_final_step': step == 'review_submit',
        'step_number': step_order.index(step) + 1 if step in step_order else 1,
        'total_steps': 7,
        'has_draft': draft is not None,
        'draft_updated_at': draft.updated_at if draft else None,
    }
    
    # Add guided tour data for first-time users
    if not request.user.service_provider_profile.venue_creation_tutorial_completed:
        context['show_guided_tour'] = True
        context['tour_steps'] = get_guided_tour_steps(step)
    
    return render(request, 'venues_app/venue_create_wizard.html', context)


def handle_wizard_ajax(request, wizard, step):
    """Handle AJAX requests for the wizard"""
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'save_progress':
            # Save progress without validation for auto-save
            form_data = {k: v for k, v in request.POST.items() if k != 'csrfmiddlewaretoken'}
            result = wizard.auto_save_progress(form_data, step)
            
            if result['success']:
                return JsonResponse({
                    'success': True,
                    'message': 'Progress saved automatically',
                    'progress_percentage': result.get('progress_percentage', 0),
                    'draft_id': result.get('draft_id'),
                    'last_updated': result.get('last_updated')
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': f'Auto-save failed: {result.get("error", "Unknown error")}'
                })
        
        elif action == 'validate_field':
            # Real-time field validation
            field_name = request.POST.get('field_name')
            field_value = request.POST.get('field_value')
            
            form = VenueCreateForm(
                data={field_name: field_value},
                user=request.user,
                current_step=step
            )
            
            validation_result = {'is_valid': True, 'errors': []}
            
            if not form.is_valid() and field_name in form.errors:
                validation_result = {
                    'is_valid': False,
                    'errors': form.errors[field_name]
                }
            
            return JsonResponse(validation_result)
    
    return JsonResponse({'success': False, 'message': 'Invalid request'})


def create_venue_from_wizard(request, wizard, form):
    """Create venue from wizard data with enhanced error handling for 7-step wizard"""
    try:
        # Get the draft with all saved data
        draft = wizard.get_draft()
        if not draft:
            messages.error(request, 'No draft data found. Please start the venue creation process again.')
            return redirect('venues_app:venue_create_wizard')

        # Create venue from draft data
        venue = create_venue_from_draft(draft, form.cleaned_data)

        if venue:
            # Clear wizard progress
            wizard.clear_progress()

            # Mark tutorial as completed
            request.user.service_provider_profile.venue_creation_tutorial_completed = True
            request.user.service_provider_profile.save(update_fields=['venue_creation_tutorial_completed'])

            # Try auto-approval if venue meets criteria
            if venue.approval_status == Venue.PENDING:
                auto_approved = venue.apply_auto_approval_if_eligible()
                if auto_approved:
                    messages.success(
                        request,
                        '🎉 Congratulations! Your venue has been automatically approved and is now live! '
                        'Customers can now find and book your services.'
                    )
                else:
                    messages.success(
                        request,
                        '✅ Your venue has been submitted for review! We\'ll notify you once it\'s approved. '
                        'In the meantime, you can continue to enhance your venue profile.'
                    )
            else:
                messages.success(
                    request,
                    '📝 Your venue has been saved as a draft. Submit it for approval when you\'re ready!'
                )

            return redirect('venues_app:venue_progress')
        else:
            messages.error(request, 'Failed to create venue. Please check your data and try again.')
            return redirect('venues_app:venue_create_wizard', step='review_submit')

    except Exception as e:
        messages.error(request, f'An error occurred while creating your venue: {str(e)}')
        return redirect('venues_app:venue_create_wizard', step='review_submit')


def create_venue_from_draft(draft, final_step_data):
    """Create a venue from the draft data"""
    try:
        # Create the venue with basic information
        venue = Venue.objects.create(
            service_provider=draft.service_provider,
            venue_name=draft.venue_name,
            short_description=draft.short_description,
            state=draft.state,
            county=draft.county,
            city=draft.city,
            street_number=draft.street_number,
            street_name=draft.street_name,
            zip_code=draft.zip_code,
            latitude=draft.latitude,
            longitude=draft.longitude,
            phone=draft.phone,
            email=draft.email,
            website_url=draft.website_url,
            instagram_url=draft.instagram_url,
            facebook_url=draft.facebook_url,
            twitter_url=draft.twitter_url,
            linkedin_url=draft.linkedin_url,
            cancellation_policy=draft.cancellation_policy,
            booking_policy=draft.booking_policy,
            special_instructions=draft.special_instructions,
            approval_status=Venue.DRAFT if final_step_data.get('venue_status') == 'draft' else Venue.PENDING
        )

        # Add categories
        if draft.categories_data:
            from ..models import Category
            categories = Category.objects.filter(id__in=draft.categories_data)
            venue.categories.set(categories)

        # Create operating hours
        if draft.operating_hours_data:
            create_operating_hours_from_data(venue, draft.operating_hours_data)

        # Create amenities
        if draft.amenities_data:
            create_amenities_from_data(venue, draft.amenities_data)

        # Create services
        if draft.services_data:
            create_services_from_data(venue, draft.services_data)

        # Create FAQs
        if draft.faqs_data:
            create_faqs_from_data(venue, draft.faqs_data)

        # Handle images
        if draft.images_data:
            create_images_from_data(venue, draft.images_data)

        return venue

    except Exception as e:
        # Log the error and return None
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to create venue from draft: {str(e)}")
        return None


def create_operating_hours_from_data(venue, hours_data):
    """Create operating hours from JSON data"""
    from ..models import OperatingHours

    for day_data in hours_data:
        if isinstance(day_data, dict):
            OperatingHours.objects.create(
                venue=venue,
                day=day_data.get('day'),
                opening=day_data.get('opening'),
                closing=day_data.get('closing'),
                is_closed=day_data.get('is_closed', False),
                is_24_hours=day_data.get('is_24_hours', False)
            )


def create_amenities_from_data(venue, amenities_data):
    """Create amenities from JSON data"""
    from ..models import VenueAmenity

    for amenity_type in amenities_data:
        VenueAmenity.objects.create(
            venue=venue,
            amenity_type=amenity_type
        )


def create_services_from_data(venue, services_data):
    """Create services from JSON data"""
    from ..models import Service

    for service_data in services_data:
        if isinstance(service_data, dict):
            Service.objects.create(
                venue=venue,
                service_title=service_data.get('title'),
                short_description=service_data.get('description'),
                price_min=service_data.get('price_min'),
                price_max=service_data.get('price_max'),
                duration_minutes=service_data.get('duration', 60)
            )


def create_faqs_from_data(venue, faqs_data):
    """Create FAQs from JSON data"""
    from ..models import VenueFAQ

    for i, faq_data in enumerate(faqs_data):
        if isinstance(faq_data, dict):
            VenueFAQ.objects.create(
                venue=venue,
                question=faq_data.get('question'),
                answer=faq_data.get('answer'),
                order=i + 1
            )


def create_images_from_data(venue, images_data):
    """Create images from JSON data"""
    # This would handle image creation from uploaded files
    # Implementation depends on how images are stored in the draft
    pass
        return redirect('venues_app:venue_create_wizard', step='final')


def get_guided_tour_steps(current_step):
    """Get guided tour steps for venue creation"""
    tour_steps = {
        'basic': [
            {
                'target': '#venue_name',
                'title': 'Venue Name',
                'content': 'Choose a memorable name that represents your business. This will be visible to customers.',
                'placement': 'bottom'
            },
            {
                'target': '#short_description',
                'title': 'Description',
                'content': 'Write a brief, compelling description of your venue and services. This helps customers understand what you offer.',
                'placement': 'bottom'
            }
        ],
        'location': [
            {
                'target': '#state',
                'title': 'Location Details',
                'content': 'Provide accurate location information. This helps customers find you and enables our location-based features.',
                'placement': 'bottom'
            }
        ],
        'categories': [
            {
                'target': '.category-selection-grid',
                'title': 'Categories',
                'content': 'Select up to 3 categories that best describe your services. This helps customers find you in search results.',
                'placement': 'top'
            }
        ],
        'contact': [
            {
                'target': '#phone',
                'title': 'Contact Information',
                'content': 'Adding contact information is optional but helps build trust with customers and improves your venue\'s visibility.',
                'placement': 'bottom'
            }
        ],
        'final': [
            {
                'target': '.venue-status-options',
                'title': 'Save or Submit',
                'content': 'Choose to save as draft for later editing, or submit for admin approval to make your venue visible to customers.',
                'placement': 'top'
            }
        ]
    }
    
    return tour_steps.get(current_step, [])


@login_required
def venue_create_view(request):
    """Legacy venue creation view - redirect to wizard"""
    return redirect('venues_app:venue_create_wizard', step='basic')


class VenueCreateView(ServiceProviderRequiredMixin, CreateView):
    """Allow service providers to create a new venue."""
    model = Venue
    form_class = VenueForm
    template_name = 'venues_app/venue_create.html'
    def get_success_url(self):
        return reverse_lazy('venues_app:provider_venue_detail', kwargs={'venue_id': self.object.id})

    def dispatch(self, request, *args, **kwargs):
        # Ensure user has service provider profile
        if not hasattr(request.user, 'service_provider_profile'):
            messages.error(request, 'You must have a service provider profile to create a venue.')
            return redirect('accounts_app:service_provider_profile')

        # Check if user already has a venue (more robust check)
        existing_venue = Venue.objects.filter(
            service_provider=request.user.service_provider_profile,
            is_deleted=False
        ).first()

        if existing_venue:
            messages.warning(request, 'You already have a venue. You can only have one venue per account. You can edit your existing venue below.')
            return redirect('venues_app:venue_edit')

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        try:
            # Double-check no venue exists before saving
            existing_venue = Venue.objects.filter(
                service_provider=self.request.user.service_provider_profile,
                is_deleted=False
            ).first()

            if existing_venue:
                messages.error(self.request, 'You already have a venue. Cannot create multiple venues.')
                return redirect('venues_app:venue_edit')

            # Save the venue
            response = super().form_valid(form)
            venue = self.object

            # Ensure venue is created with pending status
            if venue.approval_status != Venue.PENDING:
                venue.approval_status = Venue.PENDING
                venue.save(update_fields=['approval_status'])

            messages.success(
                self.request,
                f'Your venue "{venue.venue_name}" has been created successfully and is pending admin approval. '
                'You will be notified once it is reviewed.'
            )
            return response

        except Exception as e:
            messages.error(self.request, 'There was an error creating your venue. Please try again.')
            return self.form_invalid(form)

    def form_invalid(self, form):
        messages.error(
            self.request,
            'Please correct the errors below and try again. All required fields must be filled out.'
        )
        return super().form_invalid(form)


@login_required
def venue_edit_view(request):
    """Enhanced venue editing with better error handling and validation feedback."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can edit venues.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    # Store original values for change detection
    original_values = {}
    if request.method == 'POST':
        # Capture original values before form processing
        fields_to_track = [
            'venue_name', 'short_description', 'state', 'county', 'city',
            'street_number', 'street_name', 'phone', 'email', 'website_url'
        ]
        for field in fields_to_track:
            original_values[field] = getattr(venue, field, None)

    if request.method == 'POST':
        form = VenueForm(
            data=request.POST,
            files=request.FILES,
            instance=venue,
            user=request.user
        )

        if form.is_valid():
            try:
                # Handle the form submission with retry capability
                operation = lambda: handle_venue_edit_operation(request, venue, form)
                result = ErrorHandler.create_retry_operation(operation, max_retries=2)()
                
                if result.get('success'):
                    messages.success(
                        request,
                        '✅ Your venue has been updated successfully! '
                        'Changes will be visible to customers immediately.'
                    )
                    return redirect('venues_app:venue_progress')
                else:
                    # Handle validation errors with better messaging
                    error_messages = []
                    for field, errors in form.errors.items():
                        # Fix: Properly handle getting field labels
                        if field in form.fields:
                            field_label = form.fields[field].label or field.replace('_', ' ').title()
                        else:
                            field_label = field.replace('_', ' ').title()
                        for error in errors:
                            user_friendly_error = ValidationMessages.get_message(field, 'invalid')
                            error_messages.append(f"{field_label}: {user_friendly_error}")
                    
                    messages.error(
                        request,
                        f"Please fix these issues: {'; '.join(error_messages[:3])}"
                        + (f" and {len(error_messages) - 3} more..." if len(error_messages) > 3 else "")
                    )
            
            except Exception as e:
                error_info = handle_venue_creation_error(e, field_name=None, user_context={'action': 'venue_edit'})
                messages.error(request, f"{error_info['message']} {'; '.join(error_info['suggestions'][:2])}")
        else:
            # Form is not valid - show specific error messages
            error_messages = []
            for field, errors in form.errors.items():
                if field == '__all__':
                    error_messages.extend(errors)
                else:
                    field_label = form.fields[field].label or field.replace('_', ' ').title()
                    for error in errors:
                        error_messages.append(f"{field_label}: {error}")

            if error_messages:
                messages.error(
                    request,
                    'Please correct the following errors: ' + '; '.join(error_messages)
                )
            else:
                messages.error(request, 'There were errors in your venue update. Please check the form below.')
    else:
        form = VenueForm(instance=venue, user=request.user)

    # Get venue images for display
    venue_images = venue.images.filter(is_active=True).order_by('-is_primary', 'order')
    
    # Get progress information
    progress = venue.get_approval_progress()

    context = {
        'form': form,
        'venue': venue,
        'venue_images': venue_images,
        'progress': progress,
        'action': 'Edit',
    }
    return render(request, 'venues_app/venue_edit.html', context)


def handle_venue_edit_operation(request, venue, form):
    """Handle venue edit operation with enhanced validation"""
    if form.is_valid():
        # Check for significant changes that might require re-approval
        changed_fields = []
        for field_name, field_value in form.cleaned_data.items():
            if hasattr(venue, field_name):
                old_value = getattr(venue, field_name)
                if old_value != field_value:
                    changed_fields.append(field_name)
        
        # Save the venue
        updated_venue = form.save()
        
        # Check if reapproval is needed
        if updated_venue.check_reapproval_required(changed_fields):
            updated_venue.trigger_reapproval(
                f"Significant changes made to: {', '.join(changed_fields)}"
            )
            messages.info(
                request,
                '📋 Your changes have been saved, but your venue needs re-approval due to significant updates. '
                'It will remain visible while under review.'
            )
        
        return {'success': True, 'venue': updated_venue}
    else:
        return {'success': False, 'errors': form.errors}


class VenueDeleteView(ServiceProviderRequiredMixin, DeleteView):
    """Allow service providers to delete their venue."""
    model = Venue
    template_name = 'venues_app/venue_delete.html'
    success_url = reverse_lazy('venues_app:venue_create')

    def get_object(self, queryset=None):
        try:
            return self.request.user.service_provider_profile.venue
        except Venue.DoesNotExist:
            messages.error(self.request, 'You do not have a venue to delete.')
            raise

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Your venue has been deleted successfully.')
        return super().delete(request, *args, **kwargs)


# Function-based aliases
venue_create = venue_create_view
venue_edit = venue_edit_view
venue_delete = VenueDeleteView.as_view()


@login_required
def manage_services(request):
    """Allow service providers to manage their venue services."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before managing services.')
        return redirect('venues_app:venue_create')

    services = venue.services.all().select_related('service_category').order_by('service_category__name', 'service_title')
    
    # Get service categories for filtering
    from ..models import ServiceCategory
    service_categories = ServiceCategory.objects.filter(is_active=True).order_by('sort_order', 'name')
    
    # Calculate statistics
    services_active_count = services.filter(is_active=True).count()
    services_featured_count = services.filter(is_featured=True).count()
    services_categories_count = services.values('service_category').distinct().count()

    context = {
        'venue': venue,
        'services': services,
        'service_categories': service_categories,
        'max_services': MAX_SERVICES_PER_VENUE,
        'can_add_service': services.count() < MAX_SERVICES_PER_VENUE,
        'services_active_count': services_active_count,
        'services_featured_count': services_featured_count,
        'services_categories_count': services_categories_count,
    }
    return render(request, 'venues_app/manage_services.html', context)


@login_required
def manage_faqs(request):
    """Allow service providers to manage their venue FAQs with templates and proper ordering."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before adding FAQs.')
        return redirect('venues_app:venue_create')

    faqs = venue.faqs.all().order_by('order')
    can_add_faq = faqs.count() < MAX_FAQS_PER_VENUE

    # Get available FAQ templates based on venue categories
    faq_templates = []
    if venue.categories.exists():
        faq_templates = VenueFAQForm.get_template_by_venue_category(venue.categories.all())

    if request.method == 'POST':
        # Handle template application
        if 'apply_template' in request.POST:
            template_index = request.POST.get('template_index')
            if template_index and template_index.isdigit() and faq_templates:
                try:
                    template_index = int(template_index)
                    if 0 <= template_index < len(faq_templates):
                        template = faq_templates[template_index]
                        form = VenueFAQForm(initial=template)
                        messages.info(request, 'Template applied. Review and save your FAQ.')
                    else:
                        form = VenueFAQForm()
                        messages.error(request, 'Invalid template selected.')
                except (ValueError, IndexError):
                    form = VenueFAQForm()
                    messages.error(request, 'Invalid template selected.')
            else:
                form = VenueFAQForm()
                messages.warning(request, 'Please select a template to apply.')
        
        # Handle FAQ creation
        elif can_add_faq:
            form = VenueFAQForm(request.POST)
            if form.is_valid():
                faq = form.save(commit=False)
                faq.venue = venue

                # Improved ordering: find next available order or compress gaps
                existing_orders = list(venue.faqs.values_list('order', flat=True).order_by('order'))
                
                # Find the next order (fill gaps first, then increment)
                next_order = 1
                for order in existing_orders:
                    if next_order < order:
                        break  # Found a gap
                    next_order = order + 1
                
                # Ensure we don't exceed the maximum
                if next_order > MAX_FAQS_PER_VENUE:
                    messages.error(request, f'Maximum {MAX_FAQS_PER_VENUE} FAQs allowed per venue.')
                    return redirect('venues_app:manage_faqs')

                faq.order = next_order
                faq.save()
                
                # Reorder FAQs to compress gaps
                _reorder_faqs(venue)
                
                messages.success(request, 'FAQ added successfully.')
                return redirect('venues_app:manage_faqs')
        else:
            form = VenueFAQForm()
            messages.error(request, f'Maximum {MAX_FAQS_PER_VENUE} FAQs allowed per venue.')
    else:
        form = VenueFAQForm()

    context = {
        'venue': venue,
        'faqs': faqs,
        'form': form,
        'max_faqs': MAX_FAQS_PER_VENUE,
        'can_add_faq': can_add_faq,
        'faq_templates': faq_templates,
        'has_templates': bool(faq_templates),
    }
    return render(request, 'venues_app/manage_faqs.html', context)


def _reorder_faqs(venue):
    """Reorder FAQs to eliminate gaps and ensure sequential ordering."""
    faqs = venue.faqs.all().order_by('order')
    for index, faq in enumerate(faqs, 1):
        if faq.order != index:
            faq.order = index
            faq.save(update_fields=['order'])


@login_required
def delete_faq(request, faq_id):
    """Allow service providers to delete their venue FAQs with automatic reordering."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can delete FAQs.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
        faq = get_object_or_404(VenueFAQ, id=faq_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        faq.delete()
        
        # Reorder remaining FAQs to eliminate gaps
        _reorder_faqs(venue)
        
        messages.success(request, 'FAQ deleted successfully.')
        return redirect('venues_app:manage_faqs')

    context = {
        'venue': venue,
        'faq': faq,
    }
    return render(request, 'venues_app/faq_delete.html', context)


@login_required 
def reorder_faqs(request):
    """Allow service providers to manually reorder their FAQs via AJAX."""
    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Permission denied'})

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    try:
        venue = request.user.service_provider_profile.venue
        faq_orders = json.loads(request.body)
        
        # Validate the order data
        if not isinstance(faq_orders, list):
            return JsonResponse({'success': False, 'error': 'Invalid data format'})
        
        # Update FAQ orders
        with transaction.atomic():
            for item in faq_orders:
                faq_id = item.get('id')
                new_order = item.get('order')
                
                if not faq_id or not new_order:
                    continue
                    
                try:
                    faq = VenueFAQ.objects.get(id=faq_id, venue=venue)
                    faq.order = new_order
                    faq.save(update_fields=['order'])
                except VenueFAQ.DoesNotExist:
                    continue
        
        return JsonResponse({'success': True, 'message': 'FAQs reordered successfully'})
        
    except (json.JSONDecodeError, Venue.DoesNotExist):
        return JsonResponse({'success': False, 'error': 'Invalid request'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def manage_operating_hours(request):
    """Allow service providers to manage their venue operating hours with templates."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can manage operating hours.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before setting operating hours.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        form = SimplifiedOperatingHoursForm(request.POST, venue=venue)
        
        # Check if applying a template
        if 'apply_template' in request.POST:
            template_type = request.POST.get('template', '')
            if template_type:
                # Apply the selected template
                template_data = _get_template_data(template_type)
                form = SimplifiedOperatingHoursForm(initial=template_data, venue=venue)
                messages.info(request, f'Applied {template_type.replace("_", " ").title()} template. Review and save your changes.')
            else:
                messages.warning(request, 'Please select a template to apply.')
                
        elif form.is_valid():
            # Save the operating hours
            if form.save(venue):
                messages.success(request, 'Operating hours updated successfully.')
                return redirect('venues_app:provider_venue_detail', venue_id=venue.id)
            else:
                messages.error(request, 'There was an error saving your operating hours.')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = SimplifiedOperatingHoursForm(venue=venue)

    context = {
        'venue': venue,
        'form': form,
        'templates': _get_available_templates(),
    }
    return render(request, 'venues_app/manage_operating_hours.html', context)


def _get_available_templates():
    """Get available schedule templates with descriptions."""
    return [
        {
            'key': 'business_hours',
            'name': 'Business Hours',
            'description': 'Monday-Friday 9 AM - 5 PM, Closed weekends',
            'preview': 'Mon-Fri: 9:00 AM - 5:00 PM, Sat-Sun: Closed'
        },
        {
            'key': 'retail_hours',
            'name': 'Retail Hours',
            'description': 'Monday-Saturday 10 AM - 8 PM, Sunday 12 PM - 6 PM',
            'preview': 'Mon-Sat: 10:00 AM - 8:00 PM, Sun: 12:00 PM - 6:00 PM'
        },
        {
            'key': 'spa_hours',
            'name': 'Spa Hours',
            'description': 'Monday-Saturday 9 AM - 7 PM, Sunday 10 AM - 6 PM',
            'preview': 'Mon-Sat: 9:00 AM - 7:00 PM, Sun: 10:00 AM - 6:00 PM'
        },
        {
            'key': 'restaurant_hours',
            'name': 'Restaurant Hours',
            'description': 'Daily 11 AM - 10 PM',
            'preview': 'Daily: 11:00 AM - 10:00 PM'
        },
        {
            'key': 'gym_hours',
            'name': 'Gym Hours',
            'description': 'Monday-Friday 5 AM - 11 PM, Weekends 7 AM - 9 PM',
            'preview': 'Mon-Fri: 5:00 AM - 11:00 PM, Sat-Sun: 7:00 AM - 9:00 PM'
        },
        {
            'key': 'salon_hours',
            'name': 'Salon Hours',
            'description': 'Tuesday-Saturday 9 AM - 6 PM, Closed Sunday-Monday',
            'preview': 'Tue-Sat: 9:00 AM - 6:00 PM, Sun-Mon: Closed'
        },
        {
            'key': 'medical_hours',
            'name': 'Medical Hours',
            'description': 'Monday-Friday 8 AM - 6 PM, Saturday 9 AM - 1 PM',
            'preview': 'Mon-Fri: 8:00 AM - 6:00 PM, Sat: 9:00 AM - 1:00 PM, Sun: Closed'
        },
        {
            'key': 'extended_hours',
            'name': 'Extended Hours',
            'description': 'Daily 7 AM - 10 PM',
            'preview': 'Daily: 7:00 AM - 10:00 PM'
        },
        {
            'key': '24_7',
            'name': '24/7',
            'description': 'Always open, 24 hours a day',
            'preview': 'Daily: 24 Hours'
        },
    ]


def _get_template_data(template_type):
    """Get initial form data for a specific template."""
    templates = {
        'business_hours': {
            'monday_status': 'regular',
            'monday_opening': '09:00',
            'monday_closing': '17:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '09:00',
            'tuesday_closing': '17:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '09:00',
            'wednesday_closing': '17:00',
            'thursday_status': 'regular',
            'thursday_opening': '09:00',
            'thursday_closing': '17:00',
            'friday_status': 'regular',
            'friday_opening': '09:00',
            'friday_closing': '17:00',
            'saturday_status': 'closed',
            'sunday_status': 'closed',
        },
        'retail_hours': {
            'monday_status': 'regular',
            'monday_opening': '10:00',
            'monday_closing': '20:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '10:00',
            'tuesday_closing': '20:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '10:00',
            'wednesday_closing': '20:00',
            'thursday_status': 'regular',
            'thursday_opening': '10:00',
            'thursday_closing': '20:00',
            'friday_status': 'regular',
            'friday_opening': '10:00',
            'friday_closing': '20:00',
            'saturday_status': 'regular',
            'saturday_opening': '10:00',
            'saturday_closing': '20:00',
            'sunday_status': 'regular',
            'sunday_opening': '12:00',
            'sunday_closing': '18:00',
        },
        'spa_hours': {
            'monday_status': 'regular',
            'monday_opening': '09:00',
            'monday_closing': '19:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '09:00',
            'tuesday_closing': '19:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '09:00',
            'wednesday_closing': '19:00',
            'thursday_status': 'regular',
            'thursday_opening': '09:00',
            'thursday_closing': '19:00',
            'friday_status': 'regular',
            'friday_opening': '09:00',
            'friday_closing': '19:00',
            'saturday_status': 'regular',
            'saturday_opening': '09:00',
            'saturday_closing': '19:00',
            'sunday_status': 'regular',
            'sunday_opening': '10:00',
            'sunday_closing': '18:00',
        },
        'restaurant_hours': {
            'monday_status': 'regular',
            'monday_opening': '11:00',
            'monday_closing': '22:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '11:00',
            'tuesday_closing': '22:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '11:00',
            'wednesday_closing': '22:00',
            'thursday_status': 'regular',
            'thursday_opening': '11:00',
            'thursday_closing': '22:00',
            'friday_status': 'regular',
            'friday_opening': '11:00',
            'friday_closing': '22:00',
            'saturday_status': 'regular',
            'saturday_opening': '11:00',
            'saturday_closing': '22:00',
            'sunday_status': 'regular',
            'sunday_opening': '11:00',
            'sunday_closing': '22:00',
        },
        'gym_hours': {
            'monday_status': 'regular',
            'monday_opening': '05:00',
            'monday_closing': '23:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '05:00',
            'tuesday_closing': '23:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '05:00',
            'wednesday_closing': '23:00',
            'thursday_status': 'regular',
            'thursday_opening': '05:00',
            'thursday_closing': '23:00',
            'friday_status': 'regular',
            'friday_opening': '05:00',
            'friday_closing': '23:00',
            'saturday_status': 'regular',
            'saturday_opening': '07:00',
            'saturday_closing': '21:00',
            'sunday_status': 'regular',
            'sunday_opening': '07:00',
            'sunday_closing': '21:00',
        },
        'salon_hours': {
            'monday_status': 'closed',
            'tuesday_status': 'regular',
            'tuesday_opening': '09:00',
            'tuesday_closing': '18:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '09:00',
            'wednesday_closing': '18:00',
            'thursday_status': 'regular',
            'thursday_opening': '09:00',
            'thursday_closing': '18:00',
            'friday_status': 'regular',
            'friday_opening': '09:00',
            'friday_closing': '18:00',
            'saturday_status': 'regular',
            'saturday_opening': '09:00',
            'saturday_closing': '18:00',
            'sunday_status': 'closed',
        },
        'medical_hours': {
            'monday_status': 'regular',
            'monday_opening': '08:00',
            'monday_closing': '18:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '08:00',
            'tuesday_closing': '18:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '08:00',
            'wednesday_closing': '18:00',
            'thursday_status': 'regular',
            'thursday_opening': '08:00',
            'thursday_closing': '18:00',
            'friday_status': 'regular',
            'friday_opening': '08:00',
            'friday_closing': '18:00',
            'saturday_status': 'regular',
            'saturday_opening': '09:00',
            'saturday_closing': '13:00',
            'sunday_status': 'closed',
        },
        'extended_hours': {
            'monday_status': 'regular',
            'monday_opening': '07:00',
            'monday_closing': '22:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '07:00',
            'tuesday_closing': '22:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '07:00',
            'wednesday_closing': '22:00',
            'thursday_status': 'regular',
            'thursday_opening': '07:00',
            'thursday_closing': '22:00',
            'friday_status': 'regular',
            'friday_opening': '07:00',
            'friday_closing': '22:00',
            'saturday_status': 'regular',
            'saturday_opening': '07:00',
            'saturday_closing': '22:00',
            'sunday_status': 'regular',
            'sunday_opening': '07:00',
            'sunday_closing': '22:00',
        },
        '24_7': {
            'monday_status': '24hours',
            'tuesday_status': '24hours',
            'wednesday_status': '24hours',
            'thursday_status': '24hours',
            'friday_status': '24hours',
            'saturday_status': '24hours',
            'sunday_status': '24hours',
        },
    }
    
    return templates.get(template_type, {})


@login_required
def manage_amenities(request):
    """Allow service providers to manage their venue amenities."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can manage amenities.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before adding amenities.')
        return redirect('venues_app:venue_create')

    amenities = venue.amenities.all().order_by('amenity_type')
    can_add_amenity = amenities.count() < 15

    if request.method == 'POST' and can_add_amenity:
        form = VenueAmenityForm(request.POST)
        if form.is_valid():
            amenity = form.save(commit=False)
            amenity.venue = venue
            amenity.save()
            messages.success(request, 'Amenity added successfully.')
            return redirect('venues_app:manage_amenities')
    else:
        form = VenueAmenityForm()

    context = {
        'venue': venue,
        'amenities': amenities,
        'form': form,
        'can_add_amenity': can_add_amenity,
        'max_amenities': 15,
    }
    return render(request, 'venues_app/manage_amenities.html', context)


@login_required
def edit_faq(request, faq_id):
    """Allow service providers to edit their venue FAQs."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can edit FAQs.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
        faq = get_object_or_404(VenueFAQ, id=faq_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        form = VenueFAQForm(request.POST, instance=faq)
        if form.is_valid():
            form.save()
            messages.success(request, 'FAQ updated successfully.')
            return redirect('venues_app:manage_faqs')
    else:
        form = VenueFAQForm(instance=faq)

    context = {
        'venue': venue,
        'faq': faq,
        'form': form,
        'action': 'Edit',
    }
    return render(request, 'venues_app/faq_form.html', context)


@login_required
def delete_faq(request, faq_id):
    """Allow service providers to delete their venue FAQs with automatic reordering."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can delete FAQs.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
        faq = get_object_or_404(VenueFAQ, id=faq_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        faq.delete()
        
        # Reorder remaining FAQs to eliminate gaps
        _reorder_faqs(venue)
        
        messages.success(request, 'FAQ deleted successfully.')
        return redirect('venues_app:manage_faqs')

    context = {
        'venue': venue,
        'faq': faq,
    }
    return render(request, 'venues_app/faq_delete.html', context)


@login_required
def edit_amenity(request, amenity_id):
    """Allow service providers to edit their venue amenities."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can edit amenities.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
        amenity = get_object_or_404(VenueAmenity, id=amenity_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        form = VenueAmenityForm(request.POST, instance=amenity)
        if form.is_valid():
            form.save()
            messages.success(request, 'Amenity updated successfully.')
            return redirect('venues_app:manage_amenities')
    else:
        form = VenueAmenityForm(instance=amenity)

    context = {
        'venue': venue,
        'amenity': amenity,
        'form': form,
        'action': 'Edit',
    }
    return render(request, 'venues_app/amenity_form.html', context)


@login_required
def delete_amenity(request, amenity_id):
    """Allow service providers to delete their venue amenities."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can delete amenities.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
        amenity = get_object_or_404(VenueAmenity, id=amenity_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        amenity.delete()
        messages.success(request, 'Amenity deleted successfully.')
        return redirect('venues_app:manage_amenities')

    context = {
        'venue': venue,
        'amenity': amenity,
    }
    return render(request, 'venues_app/amenity_delete.html', context)


@login_required
def provider_venues(request):
    """Display the provider's venue management dashboard (single venue per provider)."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('home')

    # Get the provider's venue (should be only one)
    venue = Venue.objects.filter(
        service_provider=request.user.service_provider_profile,
        is_deleted=False
    ).prefetch_related('images', 'services', 'reviews').first()

    # If venue exists, redirect to venue detail page for management
    if venue:
        return redirect('venues_app:provider_venue_detail', venue_id=venue.id)

    # If no venue exists, show the venues list page with create option
    total_services = 0
    total_bookings = 0
    avg_rating = 0

    context = {
        'venues': [],
        'page_obj': None,
        'total_services': total_services,
        'total_bookings': total_bookings,
        'avg_rating': avg_rating,
    }
    return render(request, 'venues_app/provider/venues_list.html', context)


@login_required
def provider_venue_detail(request, venue_id):
    """Display detailed information about a specific venue for the provider."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('home')

    venue = get_object_or_404(
        Venue.objects.select_related('service_provider', 'service_provider__user')
        .prefetch_related('services', 'faqs', 'images', 'categories', 'amenities'),
        id=venue_id,
        service_provider=request.user.service_provider_profile
    )

    services = venue.services.all().order_by('service_title')
    opening_hours = venue.operating_hours_set.all().order_by('day')
    faqs = venue.faqs.all().order_by('order')
    images = venue.images.filter(is_active=True).order_by('-is_primary', 'order')
    primary_image = images.filter(is_primary=True).first()
    gallery_images = images.filter(is_primary=False)
    amenities = venue.amenities.filter(is_active=True).order_by('amenity_type')

    # Calculate price range from services
    price_range = None
    if services.exists():
        prices = [service.price_min for service in services if service.price_min]
        if prices:
            min_price = min(prices)
            max_prices = [service.price_max or service.price_min for service in services if service.price_min]
            max_price = max(max_prices) if max_prices else min_price
            price_range = f"${min_price}" if min_price == max_price else f"${min_price} - ${max_price}"

    # Check if can add more FAQs
    can_add_faq = faqs.count() < MAX_FAQS_PER_VENUE

    # Check if venue is ready for approval (has minimum required content)
    is_ready_for_approval = (
        venue.venue_name and
        venue.short_description and
        services.exists() and
        images.exists()
    )

    # Get enhanced approval workflow data
    approval_timeline = venue.get_approval_timeline()
    impact_preview = venue.get_approval_impact_preview()
    approval_guidance = venue.get_approval_guidance()
    
    # Add booking count for impact calculations
    if hasattr(venue, 'bookings'):
        venue._booking_count = venue.bookings.filter(
            status__in=['confirmed', 'pending']
        ).count()

    context = {
        'venue': venue,
        'services': services,
        'opening_hours': opening_hours,
        'faqs': faqs,
        'images': images,
        'primary_image': primary_image,
        'gallery_images': gallery_images,
        'amenities': amenities,
        'price_range': price_range,
        'can_add_faq': can_add_faq,
        'max_faqs': MAX_FAQS_PER_VENUE,
        'is_ready_for_approval': is_ready_for_approval,
        # Enhanced approval workflow data
        'approval_timeline': approval_timeline,
        'impact_preview': impact_preview,
        'approval_guidance': approval_guidance,
    }
    return render(request, 'venues_app/provider/venue_detail.html', context)


@login_required
def change_venue_status(request, venue_id):
    """Allow service providers to change their venue status (draft/pending approval)."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can change venue status.')
        return redirect('home')

    venue = get_object_or_404(
        Venue,
        id=venue_id,
        service_provider=request.user.service_provider_profile
    )

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'submit_for_approval':
            # Check if venue has minimum required content
            services = venue.services.all()
            images = venue.images.filter(is_active=True)

            if not (venue.venue_name and venue.short_description and services.exists() and images.exists()):
                messages.error(
                    request,
                    'Your venue needs at least a name, description, one service, and one image before submitting for approval.'
                )
                return redirect('venues_app:provider_venue_detail', venue_id=venue.id)

            venue.approval_status = Venue.PENDING
            venue.save(update_fields=['approval_status'])
            messages.success(
                request,
                'Your venue has been submitted for admin approval. You will be notified once it is reviewed.'
            )

        elif action == 'save_as_draft':
            venue.approval_status = Venue.DRAFT
            venue.save(update_fields=['approval_status'])
            messages.success(
                request,
                'Your venue has been saved as a draft. You can continue editing and submit for approval when ready.'
            )
        else:
            messages.error(request, 'Invalid action.')

    return redirect('venues_app:provider_venue_detail', venue_id=venue.id)


def venue_progress(request):
    """Display venue creation/setup progress for service providers."""
    return redirect('venues_app:provider_venues')


def trigger_auto_approval_check(request):
    """Trigger auto-approval check for a venue."""
    return redirect('venues_app:provider_venues')


def bulk_service_actions(request):
    """Handle bulk actions on services."""
    return redirect('venues_app:manage_services')


class ServiceCreateView(CreateView):
    """Create view for services."""
    model = Service
    fields = ['service_title', 'short_description', 'price_min']
    template_name = 'venues_app/service_form.html'


class ServiceUpdateView(UpdateView):
    """Update view for services."""
    model = Service
    fields = ['service_title', 'short_description', 'price_min']
    template_name = 'venues_app/service_form.html'


class ServiceDeleteView(DeleteView):
    """Delete view for services."""
    model = Service
    template_name = 'venues_app/service_delete.html'


def service_create(request):
    """Create a new service."""
    if not request.user.is_authenticated:
        return redirect('accounts_app:customer_login')
    
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before adding services.')
        return redirect('venues_app:venue_create')

    # Check if user has reached maximum services limit
    if venue.services.count() >= MAX_SERVICES_PER_VENUE:
        messages.error(request, f'You have reached the maximum limit of {MAX_SERVICES_PER_VENUE} services per venue.')
        return redirect('venues_app:manage_services')

    if request.method == 'POST':
        form = ServiceForm(request.POST, venue=venue)
        if form.is_valid():
            service = form.save(commit=False)
            service.venue = venue
            service.save()
            messages.success(request, f'Service "{service.service_title}" has been created successfully!')
            return redirect('venues_app:manage_services')
        else:
            # Debug: Print form errors
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = ServiceForm(venue=venue)

    context = {
        'form': form,
        'venue': venue,
        'max_services': MAX_SERVICES_PER_VENUE,
        'current_services_count': venue.services.count(),
    }
    return render(request, 'venues_app/service_create.html', context)


def service_edit(request, pk):
    """Edit an existing service."""
    if not request.user.is_authenticated:
        return redirect('accounts_app:customer_login')
    
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
        service = get_object_or_404(Service, id=pk, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        form = ServiceForm(request.POST, instance=service, venue=venue)
        if form.is_valid():
            service = form.save()
            messages.success(request, f'Service "{service.service_title}" has been updated successfully!')
            return redirect('venues_app:manage_services')
        else:
            # Debug: Print form errors
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = ServiceForm(instance=service, venue=venue)

    context = {
        'form': form,
        'service': service,
        'venue': venue,
        'object': service,  # For template compatibility
    }
    return render(request, 'venues_app/service_edit.html', context)


def service_delete(request, pk):
    """Delete an existing service."""
    if not request.user.is_authenticated:
        return redirect('accounts_app:customer_login')
    
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
        service = get_object_or_404(Service, id=pk, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        service_title = service.service_title
        service.delete()
        messages.success(request, f'Service "{service_title}" has been deleted successfully!')
        return redirect('venues_app:manage_services')

    context = {
        'service': service,
        'venue': venue,
        'object': service,  # For template compatibility
    }
    return render(request, 'venues_app/service_delete.html', context)


@login_required
def manage_venue_images(request):
    """Manage venue images."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before managing images.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        # Handle image upload via regular POST (non-AJAX)
        max_images = 5
        current_count = venue.images.filter(is_active=True).count()
        
        if current_count >= max_images:
            messages.error(request, f'Maximum {max_images} images allowed per venue.')
            return redirect('venues_app:manage_venue_images')

        form = VenueImageForm(request.POST, request.FILES)
        if form.is_valid():
            venue_image = form.save(commit=False)
            venue_image.venue = venue
            
            # Set as primary if it's the first image
            if current_count == 0:
                venue_image.is_primary = True
                
            venue_image.save()
            messages.success(request, 'Image uploaded successfully!')
            return redirect('venues_app:manage_venue_images')
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')

    images = venue.images.filter(is_active=True).order_by('-is_primary', 'order')
    form = VenueImageForm()
    max_images = 5
    can_add_image = images.count() < max_images

    context = {
        'venue': venue,
        'images': images,
        'form': form,
        'max_images': max_images,
        'can_add_image': can_add_image,
    }
    return render(request, 'venues_app/provider/manage_images.html', context)


@login_required
def upload_venue_image(request):
    """Upload venue image via AJAX."""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Only POST method allowed'})

    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Only service providers can upload images'})

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'You need to create a venue first'})

    # Check if venue has reached max images limit
    max_images = 5
    current_count = venue.images.filter(is_active=True).count()
    if current_count >= max_images:
        return JsonResponse({
            'success': False, 
            'error': f'Maximum {max_images} images allowed per venue'
        })

    form = VenueImageForm(request.POST, request.FILES)
    if form.is_valid():
        venue_image = form.save(commit=False)
        venue_image.venue = venue
        
        # Set as primary if it's the first image
        if current_count == 0:
            venue_image.is_primary = True
            
        venue_image.save()
        
        return JsonResponse({
            'success': True,
            'image_id': venue_image.id,
            'image_url': venue_image.image.url,
            'message': 'Image uploaded successfully'
        })
    else:
        return JsonResponse({
            'success': False,
            'errors': form.errors
        })


@login_required 
def set_primary_image(request, image_id):
    """Set primary venue image."""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Only POST method allowed'})

    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Only service providers can modify images'})

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Venue not found'})

    try:
        image = VenueImage.objects.get(id=image_id, is_active=True)
    except VenueImage.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Image not found'})

    # Check if the image belongs to the user's venue
    if image.venue != venue:
        return JsonResponse({'success': False, 'error': 'You can only modify images from your own venue'})

    # Set this image as primary (the model save method handles unsetting others)
    image.is_primary = True
    image.save()

    return JsonResponse({
        'success': True,
        'message': 'Primary image updated successfully'
    })


@login_required
def delete_venue_image(request, image_id):
    """Delete venue image."""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Only POST method allowed'})

    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Only service providers can delete images'})

    try:
        venue = request.user.service_provider_profile.venue
        image = get_object_or_404(VenueImage, id=image_id, venue=venue, is_active=True)
    except Venue.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Venue not found'})

    was_primary = image.is_primary
    image.delete()

    current_count = venue.images.filter(is_active=True).count()

    return JsonResponse({
        'success': True,
        'message': 'Image deleted successfully',
        'was_primary': was_primary,
        'current_count': current_count,
        'max_images': 5
    })


@login_required
def reorder_venue_images(request):
    """Reorder venue images."""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Only POST method allowed'})

    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Only service providers can reorder images'})

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Venue not found'})

    try:
        import json
        data = json.loads(request.body)
        image_orders = data.get('image_orders', [])
        
        for item in image_orders:
            image_id = item.get('id')
            new_order = item.get('order')
            
            try:
                image = VenueImage.objects.get(id=image_id, venue=venue, is_active=True)
                image.order = new_order
                image.save(update_fields=['order'])
            except VenueImage.DoesNotExist:
                continue
                
        return JsonResponse({
            'success': True,
            'message': 'Images reordered successfully'
        })
        
    except (json.JSONDecodeError, KeyError):
        return JsonResponse({'success': False, 'error': 'Invalid request data'})


def validate_venue_image_preview(request):
    """Validate venue image preview."""
    return JsonResponse({'status': 'ok'})


def reorder_venue_image(request):
    """Reorder venue image."""
    return redirect('venues_app:provider_venues')


def undo_image_action(request):
    """Undo image action."""
    return redirect('venues_app:provider_venues')


def manage_holiday_schedules(request):
    """Manage holiday schedules."""
    return redirect('venues_app:provider_venues')


def delete_holiday_schedule(request):
    """Delete holiday schedule."""
    return redirect('venues_app:provider_venues')


def validate_field_ajax(request):
    """Validate field via AJAX."""
    return JsonResponse({'status': 'ok'})


def auto_save_progress(request):
    """Auto save progress."""
    return JsonResponse({'status': 'ok'})


def sync_contact_info(request):
    """Sync contact info."""
    return redirect('venues_app:provider_venues')


def send_email_verification(request):
    """Send email verification."""
    return redirect('venues_app:provider_venues')


def verify_venue_email(request):
    """Verify venue email."""
    return redirect('venues_app:provider_venues')


def manage_venue_visibility(request):
    """Manage venue visibility."""
    return redirect('venues_app:provider_venues')


def venue_preview(request):
    """Preview venue."""
    return redirect('venues_app:provider_venues')


def update_information_freshness(request):
    """Update information freshness."""
    return redirect('venues_app:provider_venues')

