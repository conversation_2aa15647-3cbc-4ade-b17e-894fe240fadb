{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Provider Dashboard - CozyWish{% endblock %}

{% block dashboard_title %}Provider Dashboard{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* CozyWish Provider Dashboard - Minimal Clean Design */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-50: #fafafa;
        --cw-neutral-100: #f5f5f5;
        --cw-neutral-200: #e5e5e5;
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

        /* Gradients */
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Minimal Dashboard Layout */
    .dashboard-content {
        background: white !important;
        padding: 2rem;
        max-width: 1200px;
        margin: 0 auto;
    }

    /* Stats Grid - Made Smaller */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2.5rem;
    }

    .stat-item {
        background: linear-gradient(135deg, white 0%, var(--cw-accent-light) 100%);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.25rem;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: var(--cw-shadow-sm);
    }

    .stat-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        border-color: var(--cw-brand-primary);
    }

    .stat-item.featured {
        background: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
        color: white;
    }

    .stat-number {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        font-family: var(--cw-font-heading);
    }

    .stat-label {
        font-size: 0.875rem;
        font-weight: 500;
        opacity: 0.9;
        font-family: var(--cw-font-primary);
    }

    /* Content Sections */
    .content-section {
        margin-bottom: 2.5rem;
    }

    .section-title {
        font-size: 1.375rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1.5rem;
        font-family: var(--cw-font-heading);
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 0.75rem;
        color: var(--cw-brand-primary);
    }

    .section-content {
        background: white;
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.75rem;
        box-shadow: var(--cw-shadow-sm);
    }

    /* Actions Grid */
    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 1rem;
    }

    .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem 1.5rem;
        background: white;
        border: 2px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        color: var(--cw-brand-primary);
        text-decoration: none;
        font-weight: 600;
        font-family: var(--cw-font-primary);
        transition: all 0.3s ease;
        box-shadow: var(--cw-shadow-sm);
    }

    .action-btn:hover {
        background: var(--cw-brand-accent);
        border-color: var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    .action-btn.primary {
        background: var(--cw-gradient-brand-button);
        color: white;
        border-color: var(--cw-brand-primary);
    }

    .action-btn.primary:hover {
        background: var(--cw-brand-light);
        color: white;
    }

    /* Booking Items */
    .booking-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1.25rem 0;
        border-bottom: 1px solid var(--cw-neutral-200);
    }

    .booking-item:last-child {
        border-bottom: none;
    }

    .booking-info {
        flex: 1;
    }

    .booking-time {
        font-weight: 600;
        color: var(--cw-brand-primary);
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .booking-customer {
        color: var(--cw-neutral-700);
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .booking-service {
        color: var(--cw-neutral-600);
        font-size: 0.875rem;
    }

    .booking-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .booking-status {
        padding: 0.375rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .booking-status.pending {
        background: rgba(245, 158, 11, 0.1);
        color: #d97706;
        border: 1px solid rgba(245, 158, 11, 0.2);
    }

    .booking-status.confirmed {
        background: rgba(16, 185, 129, 0.1);
        color: #059669;
        border: 1px solid rgba(16, 185, 129, 0.2);
    }

    .booking-status.completed {
        background: rgba(99, 102, 241, 0.1);
        color: #6366f1;
        border: 1px solid rgba(99, 102, 241, 0.2);
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 3rem 2rem;
        color: var(--cw-neutral-600);
    }

    .empty-state-icon {
        font-size: 3rem;
        color: var(--cw-neutral-400);
        margin-bottom: 1rem;
    }

    .empty-state h5 {
        color: var(--cw-brand-primary);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        margin-bottom: 1.5rem;
    }

    /* Branded Buttons */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        color: white;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        font-family: var(--cw-font-primary);
    }

    .btn-cw-primary:hover {
        background: var(--cw-brand-light);
        color: white;
        transform: translateY(-1px);
        box-shadow: var(--cw-shadow-md);
    }

    .btn-cw-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        font-family: var(--cw-font-primary);
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-accent);
        border-color: var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        transform: translateY(-1px);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-content {
            padding: 1.5rem 1rem;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 0.75rem;
        }

        .stat-item {
            padding: 1rem;
        }

        .stat-number {
            font-size: 1.5rem;
        }

        .section-content {
            padding: 1.25rem;
        }

        .booking-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .booking-actions {
            width: 100%;
            justify-content: flex-end;
        }
    }
</style>
{% endblock %}

{% block dashboard_actions %}
<div class="d-flex align-items-center">
    {% if not venue %}
    <a href="{% url 'venues_app:venue_create' %}" class="btn btn-cw-primary" data-bs-toggle="tooltip" title="Add Venue">
        <i class="fas fa-plus me-2"></i>Add Venue
    </a>
    {% endif %}
    {% if venue %}
    <a href="{% url 'venues_app:manage_services' %}" class="btn btn-cw-secondary ms-2" data-bs-toggle="tooltip" title="Manage Services">
        <i class="fas fa-spa me-2"></i>Manage Services
    </a>
    {% endif %}
</div>
{% endblock %}

{% block dashboard_content %}
<!-- Stats Overview -->
<div class="stats-grid">
    <div class="stat-item">
        <div class="stat-number">{{ todays_bookings_count|default:0 }}</div>
        <div class="stat-label">Today's Bookings</div>
    </div>

    <div class="stat-item">
        <div class="stat-number">{{ total_bookings|default:0 }}</div>
        <div class="stat-label">Total Bookings</div>
    </div>

    <div class="stat-item">
        <div class="stat-number">${{ monthly_earnings|floatformat:0|default:0 }}</div>
        <div class="stat-label">Monthly Earnings</div>
    </div>

    <div class="stat-item">
        <div class="stat-number">{{ pending_bookings|default:0 }}</div>
        <div class="stat-label">Pending Bookings</div>
    </div>
</div>

<!-- Quick Actions -->
<div class="content-section">
    <h2 class="section-title">
        Quick Actions
    </h2>
    <div class="actions-grid">
        <a href="{% url 'dashboard_app:provider_todays_bookings' %}" class="action-btn">
            <i class="fas fa-calendar-day me-2"></i>
            Today's Schedule
        </a>
        <a href="{% url 'dashboard_app:provider_earnings_reports' %}" class="action-btn">
            <i class="fas fa-chart-line me-2"></i>
            Earnings Report
        </a>
        {% if venue %}
        <a href="{% url 'venues_app:venue_manage' %}" class="action-btn">
            <i class="fas fa-cog me-2"></i>
            Venue Settings
        </a>
        {% endif %}
        <a href="{% url 'dashboard_app:provider_team_management' %}" class="action-btn">
            <i class="fas fa-users me-2"></i>
            Team Management
        </a>
    </div>
</div>

<!-- Today's Bookings -->
<div class="content-section">
    <h2 class="section-title">
        <i class="fas fa-calendar-day me-2"></i>
        Today's Bookings
    </h2>
    {% if todays_bookings %}
    <div class="section-content">
        {% for booking in todays_bookings %}
        <div class="booking-item">
            <div class="booking-info">
                <div class="booking-time">
                    {% for item in booking.items.all %}
                        {{ item.scheduled_time|time:"g:i A" }}{% if not forloop.last %}, {% endif %}
                    {% endfor %}
                </div>
                <div class="booking-customer">
                    <i class="fas fa-user me-1"></i>
                    {{ booking.customer.get_full_name }}
                </div>
                <div class="booking-service">
                    {% for item in booking.items.all %}
                        {{ item.service.service_title }}{% if not forloop.last %}, {% endif %}
                    {% endfor %}
                </div>
            </div>
            <div class="booking-actions">
                <span class="booking-status {{ booking.status }}">{{ booking.get_status_display }}</span>
                <a href="{% url 'booking_cart_app:provider_booking_detail' booking.id %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-eye"></i>
                </a>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="fas fa-calendar-times"></i>
        </div>
        <h5>No bookings for today</h5>
        <p>Your schedule is clear for today.</p>
        <a href="{% url 'dashboard_app:provider_todays_bookings' %}" class="btn-cw-secondary">
            <i class="fas fa-calendar me-1"></i>View All Bookings
        </a>
    </div>
    {% endif %}
</div>

<!-- Performance Summary -->
<div class="content-section">
    <h2 class="section-title">
        <i class="fas fa-chart-bar me-2"></i>
        Performance Summary
    </h2>
    <div class="section-content">
        <div class="row text-center">
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-number text-success">{{ confirmed_bookings|default:0 }}</div>
                <div class="stat-label">Confirmed</div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="stat-number text-warning">{{ pending_bookings|default:0 }}</div>
                <div class="stat-label">Pending</div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-number text-info">{{ completed_bookings|default:0 }}</div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-number" style="color: var(--cw-brand-primary);">${{ total_earnings|floatformat:0|default:0 }}</div>
                <div class="stat-label">Total Earned</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block dashboard_extra_js %}
<script>
// Enhanced Provider Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeProviderDashboard();
});

function initializeProviderDashboard() {
    // Add loading states to action buttons
    setupActionButtons();
    
    // Initialize any real-time updates
    setupRealTimeUpdates();
}

function setupActionButtons() {
    const actionButtons = document.querySelectorAll('.action-btn');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add loading state to button
            const icon = this.querySelector('i');
            if (icon) {
                const originalClass = icon.className;
                icon.className = 'fas fa-spinner fa-spin me-2';
                
                // Reset after navigation
                setTimeout(() => {
                    icon.className = originalClass;
                }, 2000);
            }
        });
    });
}

function setupRealTimeUpdates() {
    // Placeholder for real-time updates functionality
    // This can be enhanced with WebSocket connections or periodic AJAX calls
    console.log('Provider dashboard real-time updates initialized');
}
</script>
{% endblock %}
